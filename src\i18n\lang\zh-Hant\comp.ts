export default {
  playlistDrawer: {
    title: '新增至播放清單',
    createPlaylist: '建立新播放清單',
    cancelCreate: '取消建立',
    create: '建立',
    playlistName: '播放清單名稱',
    privatePlaylist: '私人播放清單',
    publicPlaylist: '公開播放清單',
    createSuccess: '播放清單建立成功',
    createFailed: '播放清單建立失敗',
    addSuccess: '歌曲新增成功',
    addFailed: '歌曲新增失敗',
    private: '私人',
    public: '公開',
    count: '首歌曲',
    loginFirst: '請先登入',
    getPlaylistFailed: '取得播放清單失敗',
    inputPlaylistName: '請輸入播放清單名稱'
  },
  playlistType: {
    title: '播放清單分類',
    showAll: '顯示全部',
    hide: '隱藏部分'
  },
  recommendAlbum: {
    title: '最新專輯'
  },
  recommendSinger: {
    title: '每日推薦',
    songlist: '每日推薦清單'
  },
  recommendSonglist: {
    title: '本週最熱音樂'
  },
  searchBar: {
    login: '登入',
    toLogin: '去登入',
    logout: '登出',
    set: '設定',
    theme: '主題',
    restart: '重新啟動',
    refresh: '重新整理',
    currentVersion: '目前版本',
    searchPlaceholder: '搜尋點什麼吧...',
    zoom: '頁面縮放',
    zoom100: '標準縮放100%',
    resetZoom: '點擊重設縮放',
    zoomDefault: '標準縮放'
  },
  titleBar: {
    closeTitle: '請選擇關閉方式',
    minimizeToTray: '最小化到系統匣',
    exitApp: '退出應用程式',
    rememberChoice: '記住我的選擇',
    closeApp: '關閉應用程式'
  },
  userPlayList: {
    title: '{name}的常聽'
  },
  musicList: {
    searchSongs: '搜尋歌曲',
    noSearchResults: '沒有找到相關歌曲',
    switchToNormal: '切換到預設版面',
    switchToCompact: '切換到緊湊版面',
    playAll: '播放全部',
    collect: '收藏',
    collectSuccess: '收藏成功',
    cancelCollectSuccess: '取消收藏成功',
    operationFailed: '操作失敗',
    cancelCollect: '取消收藏',
    addToPlaylist: '新增至播放清單',
    addToPlaylistSuccess: '新增至播放清單成功',
    songsAlreadyInPlaylist: '歌曲已存在於播放清單中'
  },
  playlist: {
    import: {
      button: '播放清單匯入',
      title: '播放清單匯入',
      description: '支援透過元資料/文字/連結三種方式匯入播放清單',
      linkTab: '連結匯入',
      textTab: '文字匯入',
      localTab: '元資料匯入',
      linkPlaceholder: '請輸入播放清單連結，每行一個',
      textPlaceholder: '請輸入歌曲資訊，格式為：歌曲名 歌手名',
      localPlaceholder: '請輸入JSON格式的歌曲元資料',
      linkTips: '支援的連結來源：',
      linkTip1: '將播放清單分享至微信/微博/QQ後複製連結',
      linkTip2: '直接複製播放清單/個人主頁連結',
      linkTip3: '直接複製文章連結',
      textTips: '請輸入歌曲資訊，每行一首歌',
      textFormat: '格式：歌曲名 歌手名',
      localTips: '請新增歌曲元資料',
      localFormat: '格式範例：',
      songNamePlaceholder: '歌曲名稱',
      artistNamePlaceholder: '藝人名稱',
      albumNamePlaceholder: '專輯名稱',
      addSongButton: '新增歌曲',
      addLinkButton: '新增連結',
      importToStarPlaylist: '匯入到我喜歡的音樂',
      playlistNamePlaceholder: '請輸入播放清單名稱',
      importButton: '開始匯入',
      emptyLinkWarning: '請輸入播放清單連結',
      emptyTextWarning: '請輸入歌曲資訊',
      emptyLocalWarning: '請輸入歌曲元資料',
      invalidJsonFormat: 'JSON格式不正確',
      importSuccess: '匯入任務建立成功',
      importFailed: '匯入失敗',
      importStatus: '匯入狀態',
      refresh: '重新整理',
      taskId: '任務ID',
      status: '狀態',
      successCount: '成功數量',
      failReason: '失敗原因',
      unknownError: '未知錯誤',
      statusPending: '等待處理',
      statusProcessing: '處理中',
      statusSuccess: '匯入成功',
      statusFailed: '匯入失敗',
      statusUnknown: '未知狀態',
      taskList: '任務清單',
      taskListTitle: '匯入任務清單',
      action: '操作',
      select: '選擇',
      fetchTaskListFailed: '取得任務清單失敗',
      noTasks: '暫無匯入任務',
      clearTasks: '清除任務',
      clearTasksConfirmTitle: '確認清除',
      clearTasksConfirmContent: '確定要清除所有匯入任務記錄嗎？此操作不可恢復。',
      confirm: '確認',
      cancel: '取消',
      clearTasksSuccess: '任務清單已清除',
      clearTasksFailed: '清除任務清單失敗'
    }
  },
  settings: '設定',
  user: '使用者',
  toplist: '榜單',
  history: '收藏歷史',
  list: '播放清單',
  mv: 'MV',
  home: '首頁',
  search: '搜尋'
};
