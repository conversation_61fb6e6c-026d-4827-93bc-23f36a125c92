export default {
  playlistDrawer: {
    title: '添加到歌单',
    createPlaylist: '创建新歌单',
    cancelCreate: '取消创建',
    create: '创建',
    playlistName: '歌单名称',
    privatePlaylist: '私密歌单',
    publicPlaylist: '公开歌单',
    createSuccess: '歌单创建成功',
    createFailed: '歌单创建失败',
    addSuccess: '歌曲添加成功',
    addFailed: '歌曲添加失败',
    private: '私密',
    public: '公开',
    count: '首歌曲',
    loginFirst: '请先登录',
    getPlaylistFailed: '获取歌单失败',
    inputPlaylistName: '请输入歌单名称'
  },
  playlistType: {
    title: '歌单分类',
    showAll: '显示全部',
    hide: '隐藏一些'
  },
  recommendAlbum: {
    title: '最新专辑'
  },
  recommendSinger: {
    title: '每日推荐',
    songlist: '每日推荐列表'
  },
  recommendSonglist: {
    title: '本周最热音乐'
  },
  searchBar: {
    login: '登录',
    toLogin: '去登录',
    logout: '退出登录',
    set: '设置',
    theme: '主题',
    restart: '重启',
    refresh: '刷新',
    currentVersion: '当前版本',
    searchPlaceholder: '搜索点什么吧...',
    zoom: '页面缩放',
    zoom100: '标准缩放100%',
    resetZoom: '点击重置缩放',
    zoomDefault: '标准缩放'
  },
  titleBar: {
    closeTitle: '请选择关闭方式',
    minimizeToTray: '最小化到托盘',
    exitApp: '退出应用',
    rememberChoice: '记住我的选择',
    closeApp: '关闭应用'
  },
  userPlayList: {
    title: '{name}的常听'
  },
  musicList: {
    searchSongs: '搜索歌曲',
    noSearchResults: '没有找到相关歌曲',
    switchToNormal: '切换到默认布局',
    switchToCompact: '切换到紧凑布局',
    playAll: '播放全部',
    collect: '收藏',
    collectSuccess: '收藏成功',
    cancelCollectSuccess: '取消收藏成功',
    operationFailed: '操作失败',
    cancelCollect: '取消收藏',
    addToPlaylist: '添加到播放列表',
    addToPlaylistSuccess: '添加到播放列表成功',
    songsAlreadyInPlaylist: '歌曲已存在于播放列表中'
  },
  playlist: {
    import: {
      button: '歌单导入',
      title: '歌单导入',
      description: '支持通过元数据/文字/链接三种方式导入歌单',
      linkTab: '链接导入',
      textTab: '文字导入',
      localTab: '元数据导入',
      linkPlaceholder: '请输入歌单链接，每行一个',
      textPlaceholder: '请输入歌曲信息，格式为：歌曲名 歌手名',
      localPlaceholder: '请输入JSON格式的歌曲元数据',
      linkTips: '支持的链接来源：',
      linkTip1: '将歌单分享到微信/微博/QQ后复制链接',
      linkTip2: '直接复制歌单/个人主页链接',
      linkTip3: '直接复制文章链接',
      textTips: '请输入歌曲信息，每行一首歌',
      textFormat: '格式：歌曲名 歌手名',
      localTips: '请添加歌曲元数据',
      localFormat: '格式示例：',
      songNamePlaceholder: '歌曲名称',
      artistNamePlaceholder: '艺术家名称',
      albumNamePlaceholder: '专辑名称',
      addSongButton: '添加歌曲',
      addLinkButton: '添加链接',
      importToStarPlaylist: '导入到我喜欢的音乐',
      playlistNamePlaceholder: '请输入歌单名称',
      importButton: '开始导入',
      emptyLinkWarning: '请输入歌单链接',
      emptyTextWarning: '请输入歌曲信息',
      emptyLocalWarning: '请输入歌曲元数据',
      invalidJsonFormat: 'JSON格式不正确',
      importSuccess: '导入任务创建成功',
      importFailed: '导入失败',
      importStatus: '导入状态',
      refresh: '刷新',
      taskId: '任务ID',
      status: '状态',
      successCount: '成功数量',
      failReason: '失败原因',
      unknownError: '未知错误',
      statusPending: '等待处理',
      statusProcessing: '处理中',
      statusSuccess: '导入成功',
      statusFailed: '导入失败',
      statusUnknown: '未知状态',
      taskList: '任务列表',
      taskListTitle: '导入任务列表',
      action: '操作',
      select: '选择',
      fetchTaskListFailed: '获取任务列表失败',
      noTasks: '暂无导入任务',
      clearTasks: '清除任务',
      clearTasksConfirmTitle: '确认清除',
      clearTasksConfirmContent: '确定要清除所有导入任务记录吗？此操作不可恢复。',
      confirm: '确认',
      cancel: '取消',
      clearTasksSuccess: '任务列表已清除',
      clearTasksFailed: '清除任务列表失败'
    }
  },
  settings: '设置',
  user: '用户',
  toplist: '榜单',
  history: '收藏历史',
  list: '歌单',
  mv: 'MV',
  home: '首页',
  search: '搜索'
};
