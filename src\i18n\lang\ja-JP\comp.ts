export default {
  playlistDrawer: {
    title: 'プレイリストに追加',
    createPlaylist: '新しいプレイリストを作成',
    cancelCreate: '作成をキャンセル',
    create: '作成',
    playlistName: 'プレイリスト名',
    privatePlaylist: 'プライベートプレイリスト',
    publicPlaylist: 'パブリックプレイリスト',
    createSuccess: 'プレイリストの作成に成功しました',
    createFailed: 'プレイリストの作成に失敗しました',
    addSuccess: '楽曲の追加に成功しました',
    addFailed: '楽曲の追加に失敗しました',
    private: 'プライベート',
    public: 'パブリック',
    count: '曲',
    loginFirst: 'まずログインしてください',
    getPlaylistFailed: 'プレイリストの取得に失敗しました',
    inputPlaylistName: 'プレイリスト名を入力してください'
  },
  playlistType: {
    title: 'プレイリストカテゴリ',
    showAll: 'すべて表示',
    hide: '一部を非表示'
  },
  recommendAlbum: {
    title: '最新アルバム'
  },
  recommendSinger: {
    title: '毎日のおすすめ',
    songlist: '毎日のおすすめリスト'
  },
  recommendSonglist: {
    title: '今週の人気音楽'
  },
  searchBar: {
    login: 'ログイン',
    toLogin: 'ログインへ',
    logout: 'ログアウト',
    set: '設定',
    theme: 'テーマ',
    restart: '再起動',
    refresh: '更新',
    currentVersion: '現在のバージョン',
    searchPlaceholder: '何かを検索してみましょう...',
    zoom: 'ページズーム',
    zoom100: '標準ズーム100%',
    resetZoom: 'クリックしてズームをリセット',
    zoomDefault: '標準ズーム'
  },
  titleBar: {
    closeTitle: '閉じる方法を選択してください',
    minimizeToTray: 'トレイに最小化',
    exitApp: 'アプリを終了',
    rememberChoice: '選択を記憶する',
    closeApp: 'アプリを閉じる'
  },
  userPlayList: {
    title: '{name}のよく聞く音楽'
  },
  musicList: {
    searchSongs: '楽曲を検索',
    noSearchResults: '関連する楽曲が見つかりませんでした',
    switchToNormal: 'デフォルトレイアウトに切り替え',
    switchToCompact: 'コンパクトレイアウトに切り替え',
    playAll: 'すべて再生',
    collect: 'お気に入り',
    collectSuccess: 'お気に入りに追加しました',
    cancelCollectSuccess: 'お気に入りから削除しました',
    operationFailed: '操作に失敗しました',
    cancelCollect: 'お気に入りから削除',
    addToPlaylist: 'プレイリストに追加',
    addToPlaylistSuccess: 'プレイリストに追加しました',
    songsAlreadyInPlaylist: '楽曲は既にプレイリストに存在します'
  },
  playlist: {
    import: {
      button: 'プレイリストインポート',
      title: 'プレイリストインポート',
      description: 'メタデータ/テキスト/リンクの3つの方法でプレイリストをインポートできます',
      linkTab: 'リンクインポート',
      textTab: 'テキストインポート',
      localTab: 'メタデータインポート',
      linkPlaceholder: 'プレイリストのリンクを入力してください（1行に1つ）',
      textPlaceholder: '楽曲情報を入力してください。形式：楽曲名 アーティスト名',
      localPlaceholder: 'JSON形式の楽曲メタデータを入力してください',
      linkTips: 'サポートされているリンクソース：',
      linkTip1: 'プレイリストをWeChat/Weibo/QQでシェアした後、リンクをコピー',
      linkTip2: 'プレイリスト/個人ページのリンクを直接コピー',
      linkTip3: '記事のリンクを直接コピー',
      textTips: '楽曲情報を入力してください（1行に1曲）',
      textFormat: '形式：楽曲名 アーティスト名',
      localTips: '楽曲メタデータを追加してください',
      localFormat: '形式例：',
      songNamePlaceholder: '楽曲名',
      artistNamePlaceholder: 'アーティスト名',
      albumNamePlaceholder: 'アルバム名',
      addSongButton: '楽曲を追加',
      addLinkButton: 'リンクを追加',
      importToStarPlaylist: 'お気に入りの音楽にインポート',
      playlistNamePlaceholder: 'プレイリスト名を入力してください',
      importButton: 'インポート開始',
      emptyLinkWarning: 'プレイリストのリンクを入力してください',
      emptyTextWarning: '楽曲情報を入力してください',
      emptyLocalWarning: '楽曲メタデータを入力してください',
      invalidJsonFormat: 'JSON形式が正しくありません',
      importSuccess: 'インポートタスクの作成に成功しました',
      importFailed: 'インポートに失敗しました',
      importStatus: 'インポート状況',
      refresh: '更新',
      taskId: 'タスクID',
      status: 'ステータス',
      successCount: '成功数',
      failReason: '失敗理由',
      unknownError: '不明なエラー',
      statusPending: '処理待ち',
      statusProcessing: '処理中',
      statusSuccess: 'インポート成功',
      statusFailed: 'インポート失敗',
      statusUnknown: '不明なステータス',
      taskList: 'タスクリスト',
      taskListTitle: 'インポートタスクリスト',
      action: '操作',
      select: '選択',
      fetchTaskListFailed: 'タスクリストの取得に失敗しました',
      noTasks: 'インポートタスクがありません',
      clearTasks: 'タスクをクリア',
      clearTasksConfirmTitle: 'クリア確認',
      clearTasksConfirmContent:
        'すべてのインポートタスク記録をクリアしますか？この操作は元に戻せません。',
      confirm: '確認',
      cancel: 'キャンセル',
      clearTasksSuccess: 'タスクリストをクリアしました',
      clearTasksFailed: 'タスクリストのクリアに失敗しました'
    }
  },
  settings: '設定',
  user: 'ユーザー',
  toplist: 'ランキング',
  history: 'お気に入り履歴',
  list: 'プレイリスト',
  mv: 'MV',
  home: 'ホーム',
  search: '検索'
};
